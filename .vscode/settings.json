{
  // 设置 Prettier 为多种文件类型的默认格式化程序
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  // 设置 Volar 为 .vue 文件的默认格式化程序
  "[vue]": {
    "editor.defaultFormatter": "Vue.volar"
  },
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[json]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[less]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[css]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  // 保存时自动格式化
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    // 保存时自动执行 ESLint 修复(eslint --fix)
    "source.fixAll.eslint": "explicit",
    // 保存时自动删除未使用的导入
    "source.organizeImports": "explicit"
  }
}
