import globals from 'globals'
import fs from 'fs'
import path from 'path'
import js from '@eslint/js'
import tseslint from 'typescript-eslint'
import pluginVue from 'eslint-plugin-vue'
import pluginPrettierRecommended from 'eslint-plugin-prettier/recommended'
import vueParser from 'vue-eslint-parser'

// 尝试加载 unplugin-auto-import 的 ESLint 配置文件
// eslint-disable-next-line @typescript-eslint/no-explicit-any
let autoImportRules = { globals: {} }
const autoImportPath = path.resolve(import.meta.dirname, '.eslintrc-auto-import.json')
if (fs.existsSync(autoImportPath)) {
  autoImportRules = JSON.parse(fs.readFileSync(autoImportPath, 'utf-8'))
}

export default [
  {
    ignores: [
      'dist/*',
      'node_modules/*',
      'public/*',
      'eslint.config.js',
      '.eslintrc-auto-import.json',
    ],
  },
  js.configs.recommended,
  ...tseslint.configs.recommendedTypeChecked,
  ...pluginVue.configs['flat/recommended'],
  // 针对所有相关文件的核心配置
  {
    languageOptions: {
      globals: {
        ...globals.browser,
        ...globals.node,
        ...autoImportRules.globals,
        defineProps: 'readonly',
        defineEmits: 'readonly',
        defineExpose: 'readonly',
        withDefaults: 'readonly',
      },
      parserOptions: {
        project: ['./tsconfig.app.json', './tsconfig.node.json'],
        tsconfigRootDir: import.meta.dirname,
        extraFileExtensions: ['.vue'],
      },
    },
    rules: {
      '@typescript-eslint/no-unused-vars': 'error',
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/no-unsafe-argument': 'off',
      'vue/multi-word-component-names': 'off',
      '@typescript-eslint/no-unsafe-assignment': 'off', // 禁止将 any 类型的值赋值给其他变量
      '@typescript-eslint/no-unsafe-member-access': 'off', // 禁止对 any 类型的值进行属性访问
      '@typescript-eslint/no-unsafe-return': 'off', // 禁止返回 any 类型的值
    },
  },
  // 为 vue 文件启用 vue 解析器
  {
    files: ['**/*.vue'],
    languageOptions: {
      parser: vueParser,
      parserOptions: {
        parser: '@typescript-eslint/parser',
        project: ['./tsconfig.app.json', './tsconfig.node.json'],
        tsconfigRootDir: import.meta.dirname,
        extraFileExtensions: ['.vue'],
      },
    },
  },
  // 为配置文件（如 vite.config.ts）禁用特定的类型检查规则
  {
    files: ['eslint.config.js', 'vite.config.ts', 'postcss.config.cjs'],
    languageOptions: {
      parserOptions: {
        project: undefined,
      },
    },
    rules: {
      // 因为这些文件不一定包含在 tsconfig.app.json 的 project 中，
      // 所以全局启用的类型检查规则可能会在这些文件上报错。
      '@typescript-eslint/no-unsafe-call': 'off',
      '@typescript-eslint/no-unsafe-argument': 'off',
    },
  },
  // Prettier 规则优先级最高, 放在最后覆盖前面同类规则
  pluginPrettierRecommended,
]
