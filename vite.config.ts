import { VantResolver } from '@vant/auto-import-resolver'
import vue from '@vitejs/plugin-vue'
import path from 'path'
import postcssPxToViewport from 'postcss-px-to-viewport-8-plugin'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { defineConfig, loadEnv } from 'vite'

export default defineConfig(({ mode }) => {
  // const env = loadEnv(mode, process.cwd())

  return {
    plugins: [
      vue(),
      AutoImport({
        dts: 'src/types/plugin/autoImport.d.ts',
        resolvers: [VantResolver()],
        eslintrc: {
          // 生成ESLint的配置文件, 防止 vant 的 api 在 commits 时 eslint 报错
          enabled: true,
        },
      }),
      Components({
        dts: 'src/types/plugin/components.d.ts',
        resolvers: [VantResolver()],
      }),
    ],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
      },
    },
    css: {
      postcss: {
        plugins: [
          postcssPxToViewport({
            viewportWidth: 375, // 设计稿的视口宽度
            unitPrecision: 3, // 单位转换后保留的精度
            viewportUnit: 'vw', // 希望使用的视口单位
            selectorBlackList: ['.ignore', '.hairlines'], // 需要忽略的CSS选择器，不会转为视口单位，使用原有的px
            minPixelValue: 1, // 设置最小的转换数值，如果小于等于1px则不转换
            mediaQuery: false, // 媒体查询里的px是否转换
          }),
        ],
      },
    },
  }
})
