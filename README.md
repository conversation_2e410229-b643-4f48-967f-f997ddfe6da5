# Vue3 H5 项目模板

基于 Vue3 + TypeScript + Vite 打造的高效移动端 H5 项目模板，集成主流开发能力与最佳实践，助力业务快速上线与高质量迭代。

## 🧐 项目背景

> [背景详细介绍](https://alidocs.dingtalk.com/i/nodes/m9bN7RYPWdlg1xxBsoe4Be9BWZd1wyK0?iframeQuery=utm_source%3Dportal%26utm_medium%3Dportal_recent&rnd=0.17496229498941662)

随着业务发展，原有的 `xdf-app-parent-h5` 项目（MPA 架构）逐渐暴露出若干严重问题：

- **性能瓶颈**: 公共依赖包 `chunk-vendors.js` 体积膨胀(50M)，导致首屏加载时间极长，用户体验差，且造成了严重的资源浪费和缓存利用率低下的问题。
- **维护困境**: 多业务、多团队共用一个项目，导致代码高度耦合、依赖混乱，项目构建耗时，新人上手和问题定位难度大。
- **架构老旧**: Vue 2 技术栈已进入维护期，且 MPA 架构在页面切换、状态共享等方面无法满足现代 H5 应用对流畅体验的要求。

为从根本上解决以上痛点，我们创建了这套标准化的 SPA 项目模板。其核心目标是为新业务提供一个独立的、轻量化的、高性能的开发起点，确保每个项目都只包含其自身所需的最小资源集，从而实现极致的加载性能和高效的开发维护。

---

## ✨ 项目亮点

- ⚡ **极速开发体验**：基于 Vite，提供毫秒级的热模块重载（HMR），开发体验流畅。
- 🍕 **TypeScript 全量支持**：提供完整的类型定义，增强代码健壮性和可维护性。
- 🍍 **Pinia 状态管理**：Vue 官方推荐的状态管理器，轻量、易用且对 TypeScript 支持友好。
- ✨ **Vant 4 组件库**：集成主流的移动端 UI 组件库，并已配置好按需引入。
- 🌀 **自动化按需引入**：通过 `unplugin-auto-import` 和 `unplugin-vue-components` 自动引入 Vue API 和 Vant 组件，简化代码。
- 🧩 **集成 Moonbridge 桥能力**：内置 `@xdf/h5-app-moonbridge`，便于与 App 进行原生交互。
- 🛡️ **严格的代码规范**：集成 ESLint 和 Prettier，确保代码风格统一。
- 🛠️ **Git 提交工作流**：通过 Husky 和 lint-staged，在提交前自动执行代码检查和格式化，保证入库代码质量。
- 🧰 **Axios 封装**：内置 Axios，在 `src/utils/request.ts` 中进行统一封装，便于管理 API 请求。
- 📱 **vw 视口适配**：基于 `postcss-px-to-viewport-8-plugin` 自动将 `px` 转换为 `vw`，实现移动端自适应布局。
- 🏷️ **多环境配置**：内置 `test`、`uat`、`prod` 多套环境配置，通过 `vite --mode <env>` 命令轻松切换。

---

## 🏗️ 技术栈

| 类别           | 主要依赖                                                                                              |
| -------------- | ----------------------------------------------------------------------------------------------------- |
| **核心框架**   | `vue@3`, `vue-router@4`, `pinia@3`                                                                    |
| **构建工具**   | `vite@6`, `vue-tsc`                                                                                   |
| **UI 组件库**  | `vant@4`                                                                                              |
| **HTTP 请求**  | `axios`                                                                                               |
| **JS/TS 工具** | `typescript`, `qs`, `js-cookie`                                                                       |
| **CSS 相关**   | `less`, `postcss-px-to-viewport-8-plugin`                                                             |
| **代码规范**   | `eslint`, `prettier`, `@typescript-eslint/parser`, `eslint-plugin-vue`                                |
| **Git 工作流** | `husky`, `lint-staged`                                                                                |
| **Vite 插件**  | `@vitejs/plugin-vue`, `unplugin-auto-import`, `unplugin-vue-components`, `@vant/auto-import-resolver` |
| **业务集成**   | `@xdf/h5-app-moonbridge`                                                                              |

---

## 📂 目录结构

```
.
├── .husky/                       # Husky 配置文件，用于 Git Hooks
├── .vscode/                      # VSCode 编辑器相关配置
├── dist/                         # 项目打包后的产物目录
├── public/                       # 公共静态资源，此目录下的文件不会被打包处理
├── src/                          # 项目核心源码目录
│   ├── assets/                   # 静态资源，如 CSS, images, fonts，会被 Vite 处理
│   ├── components/               # 全局通用 Vue 组件
│   ├── router/                   # Vue Router 路由配置
│   ├── store/                    # Pinia 状态管理模块
│   ├── types/                    # TypeScript 类型定义文件
│   ├── utils/                    # 通用工具函数模块（如 request, bridge 等）
│   ├── views/                    # 页面级别 Vue 组件
│   ├── App.vue                   # Vue 根组件
│   ├── main.ts                   # 应用入口文件
│   └── vite-env.d.ts             # 用于让 TypeScript 识别 Vite 的环境变量类型
├── .editorconfig                 # 统一不同编辑器的编码风格
├── .env.*                        # 多环境配置文件
├── .eslint.config.js             # ESLint 配置文件 (新版扁平化配置)
├── .eslintrc-auto-import.json    # unplugin-auto-import 自动生成的 ESLint 配置
├── .gitignore                    # Git 忽略文件配置
├── .npmrc                        # NPM/PNPM 配置文件，可指定 registry
├── .prettierignore               # Prettier 格式化忽略文件配置
├── .prettierrc.json              # Prettier 格式化工具配置文件
├── index.html                    # 应用入口 HTML 文件
├── package.json                  # 项目依赖与脚本配置
├── pnpm-lock.yaml                # PNPM 锁定的依赖版本文件
├── tsconfig.json                 # TypeScript 整体项目配置文件
├── tsconfig.app.json             # TypeScript 应用源码（src）相关配置
├── tsconfig.node.json            # TypeScript Node.js 环境相关配置（如 vite.config.ts）
└── vite.config.ts                # Vite 核心配置文件
```

---

## 🚀 快速开始

### 环境要求

- **Node.js**: `20.14.0` 保持和最新的流水线Node版本一致
- **pnpm**

> 请确保你的开发环境符合 `package.json` 中 `engines` 字段的要求。

### 安装与启动

```bash
# 1. 克隆项目到本地
<NAME_EMAIL>:home-edu/web_reactor_group/vue3-h5-template.git

# 2. 进入项目目录
cd vue3-h5-template

# 3. 安装依赖 (强烈推荐使用 pnpm)
pnpm install

# 4. 启动本地开发环境
pnpm dev

# 5. 浏览器访问 http://localhost:5173 (或终端提示的地址)
```

---

## 🛠️ 常用脚本

我们提供了一系列内置脚本来支持项目的开发和维护：

| 命令                | 说明                                                            |
| ------------------- | --------------------------------------------------------------- |
| `pnpm dev`          | 启动本地开发服务器。                                            |
| `pnpm dev:test`     | 在 `test` 环境下启动本地开发服务器。                            |
| `pnpm build:prod`   | 构建生产环境的包（其他环境如 `build:test`, `build:uat` 同理）。 |
| `pnpm preview`      | 在本地预览生产环境构建出的包。                                  |
| `pnpm lint`         | 执行代码风格检查（ESLint + Prettier）。                         |
| `pnpm lint:fix`     | 自动修复可修复的代码风格问题。                                  |
| `pnpm format:write` | 使用 Prettier 格式化所有代码。                                  |
| `pnpm prepare`      | （通常无需手动执行）安装 Git Hooks (Husky)。                    |

---

## ⚙️ 配置说明

- **环境变量**:
  项目根目录下的 `.env.test`, `.env.uat`, `.env.prod` 文件分别对应不同环境的配置。Vite 会根据启动命令（`--mode <env>`）自动加载相应的配置文件。你可以在这些文件中定义 `VITE_APP_BASE_API` 等变量。

- **路径别名**:
  已在 `vite.config.ts` 和 `tsconfig.json` 中配置路径别名 `@` 指向 `src` 目录，方便在代码中引入模块，例如：`import Home from '@/views/Home.vue'`。

- **VW 适配**:
  Vite 已配置 `postcss-px-to-viewport-8-plugin`。插件会把设计稿中的 `px` 单位自动转换为 `vw`。
  - **设计稿基准宽度**: `375px`。
  - 如果某个 `px` 单位不希望被转换，可以在该 CSS 选择器后添加 `.ignore` 类，或在样式中写成 `Px` 或 `PX`。

---

## 📚 主要能力与用法

- **Vant 组件**:
  得益于 `unplugin-vue-components`，你无需手动 `import` Vant 组件，可以直接在 `.vue` 文件的模板中使用，例如 `<van-button>`。

- **Vue/Vue-Router API**:
  得益于 `unplugin-auto-import`，常用的 Vue API（如 `ref`, `computed`, `onMounted`）和 Vue-Router API（如 `useRouter`, `useRoute`）也无需手动 `import`，可直接在 `<script setup>` 中使用。

- **Moonbridge 桥接**:
  在需要与 App 交互的组件中，可以通过如 `import { getStatusBarHeight } from '@/utils/bridge'` 的方式来引入并使用桥接能力。

---

## 🧑‍💻 代码规范与开发约定

- **代码风格**:
  遵循项目配置的 ESLint 和 Prettier 规则。推荐在 VSCode 中安装 `ESLint` 和 `Prettier` 插件，并开启保存时自动格式化。

- **Git 提交**:
  当执行 `git commit` 时，`lint-staged` 会自动运行，对暂存区的文件执行代码规范检查和格式化，确保所有入库代码都符合规范。

- **分支管理**:
  建议遵循 `Git Flow` 或 `GitHub Flow` 等主流分支管理策略。例如：
  - `main`/`master`: 主分支，用于生产环境部署。
  - `feat/xxx`: 功能开发分支。
  - `fix/xxx`: Bug修复分支。

---

## 🤝 问题或建议

本模板由 <EMAIL> 维护。如果你有任何问题或建议，欢迎联系我。
