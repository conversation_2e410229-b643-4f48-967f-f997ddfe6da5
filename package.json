{"name": "ai-teacher-assistant", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite --host", "dev:test": "vite --host --mode test", "dev:uat": "vite --host --mode uat", "dev:prod": "vite --host --mode prod", "build": "vue-tsc -b && vite build --mode prod", "build:test": "vue-tsc -b && vite build --mode test", "build:uat": "vue-tsc -b && vite build --mode uat", "build:prod": "vue-tsc -b && vite build --mode prod", "preview": "vite preview", "format:check": "prettier --check . --ignore-path .prettierignore", "format:write": "prettier --write . --ignore-path .prettierignore", "lint:script": "eslint .", "lint:script:fix": "eslint . --fix", "lint": "pnpm format:check && pnpm lint:script", "lint:fix": "pnpm format:write && pnpm lint:script:fix", "prepare": "husky"}, "engines": {"node": "20.14.0"}, "lint-staged": {"*.{js,ts,vue}": ["eslint"], "*.{json,css,less,md}": ["prettier --check"]}, "packageManager": "pnpm", "dependencies": {"@xdf/h5-app-moonbridge": "^1.1.4", "axios": "^1.9.0", "js-cookie": "^3.0.5", "pinia": "^3.0.3", "qs": "^6.14.0", "vant": "^4.9.19", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@eslint/js": "^9.28.0", "@types/js-cookie": "^3.0.6", "@types/node": "^22.15.29", "@types/qs": "^6.14.0", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "@vant/auto-import-resolver": "^1.3.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-vue": "^10.1.0", "globals": "^16.2.0", "husky": "^9.1.7", "less": "^4.3.0", "lint-staged": "15.5.2", "postcss-px-to-viewport-8-plugin": "^1.2.5", "prettier": "^3.5.3", "typescript": "~5.8.3", "typescript-eslint": "^8.33.1", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.7.0", "vite": "^6.3.5", "vue-eslint-parser": "^10.1.3", "vue-tsc": "^2.2.8"}}