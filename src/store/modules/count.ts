import { defineStore } from 'pinia'

export const useCountStore = defineStore('count', {
  state: () => ({
    count: 0,
  }),
  getters: {
    doubleCounter: (state) => {
      return state.count * 2
    },
    doubleCounterPlusOne: (state) => {
      return state.count * 2 + 1
    },
  },
  actions: {
    reset() {
      this.count = 0
    },
    increment() {
      this.count++
      console.log('increment==> ', this.count)
    },
  },
})
