<script setup lang="ts">
import { getStatusBarHeight } from '@/utils/bridge'
import { ref } from 'vue'
import { useCountStore } from '../../store/modules/count'
import { storeToRefs } from 'pinia'
const statusBarHeight = ref({})

const handleGetStatusBarHeight = () => {
  getStatusBarHeight((res) => {
    statusBarHeight.value = res
  })
}

const a = '2'
console.log('HelloWorld App', a)

const env = import.meta.env

console.log('VITE_APP_ENV==> ', import.meta.env.VITE_APP_ENV)
console.log('VITE_APP_BASE_API==> ', import.meta.env.VITE_APP_BASE_API)

const testMessage = () => {
  showToast('测试 button 成功')
}

const countStore = useCountStore()
const { count, doubleCounter, doubleCounterPlusOne } = storeToRefs(countStore)
console.log('countStore==> ', count)
</script>

<template>
  <div class="app-container">
    <h2>1. 多环境变量测试</h2>
    <p>当前环境：{{ env.VITE_APP_ENV }}</p>
    <p>当前环境API地址: {{ env.VITE_APP_BASE_API }}</p>

    <h2>2. 桥接测试</h2>
    <button @click="handleGetStatusBarHeight">获取状态栏高度{{ statusBarHeight }}</button>

    <h2>3. px to viewport 测试</h2>
    <div class="test-container">
      <p>测试 px to viewport</p>
    </div>

    <h2>4. 组件测试</h2>
    <van-icon name="chat-o" badge="99+" /> <br />
    <van-icon name="apps-o" /> <br />
    <van-button type="primary" @click="testMessage">message test</van-button>

    <h2>5. pinia 测试</h2>
    <p key="count">count: {{ count }}</p>
    <p>doubleCounter: {{ doubleCounter }}</p>
    <p>doubleCounterPlusOne: {{ doubleCounterPlusOne }}</p>
    <van-button type="primary" @click="countStore.reset">reset</van-button>
    <van-button type="primary" @click="countStore.increment">increment</van-button>
  </div>
</template>

<style scoped lang="less">
.app-container {
  padding: 8px;

  .test-container {
    padding: 20px;
    width: 200px;
    height: 200px;
    background-color: pink;
    border-radius: 10px;
  }
}
</style>
./store/modules/count
