import Cookie from 'js-cookie'

/**
 * 设置 Cookie
 * @param {String} name <PERSON>ie 键
 * @param {String} value Cookie 值
 * @param {Object} options Cookie 选项
 */
export const setCookie = (name: string, value: string, options: any) => {
  Cookie.set(name, value, options)
}

/**
 * 获取 Cookie
 * @param {String} name Cookie 键
 */
export const getCookie = (name: string) => {
  return Cookie.get(name)
}

/**
 * 删除 Cookie
 * @param {String} name Cookie 键
 * @param {Object} options Cookie 选项
 */
export const removeCookie = (name: string, options: any) => {
  Cookie.remove(name, options)
}
