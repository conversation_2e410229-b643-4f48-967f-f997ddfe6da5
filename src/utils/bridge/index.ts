import xdfMoonBridge from '@xdf/h5-app-moonbridge'
import type { StatusBarHeightCallback } from './type.d'
import { parseURLParams } from '@/utils/common'
import { getCookie } from '@/utils/cookie'

/**
 * 获取设备顶部状态栏、底部安全区域的高度
 * @param {StatusBarHeightCallback} callback 回调函数
 * @example
 * getStatusBarHeight((res) => {
 *   console.log(res) // { code: 0, message: 'success', data: { top: 20, bottom: 20 } }
 * })
 */
export const getStatusBarHeight = (callback: StatusBarHeightCallback) => {
  return xdfMoonBridge.nativeModule.containerViewInset({}, callback)
}

/**
 * 获取应用信息
 */
export const getAppInfo = () => {
  const { appVersion = '' } = parseURLParams()
  const appInfo = {
    appId: '3027D9E5-0C09-4AD3-86F0-6C678B7826A4', // appId 是固定的不要修改
    appVersion: getCookie('appVersion') || appVersion,
  }
  return appInfo
}

/**
 * 获取用户信息
 */
export const getUserInfo = () => {
  const {
    accessToken = '',
    currentStudentCode = '',
    e2e = '',
    e2mf = '',
    teacherEmail = '',
  } = parseURLParams()

  const userInfo = {
    accessToken: accessToken || getCookie('accessToken'),
    currentStudentCode:
      getCookie('currentStudentCode') || currentStudentCode || getCookie('studentCode'),
    U2AT: getCookie('U2AT') || '',
    e2mf: e2mf || getCookie('e2mf') || '',
    e2e: e2e || getCookie('e2e') || '',
    eventApp: navigator.userAgent.includes('XdfWoXueTeacher') ? 'teacher' : 'student',
    teacherEmail: getCookie('teacherEmail') || teacherEmail,
    studentCode: '',
  }
  userInfo.studentCode = userInfo.currentStudentCode as string
  return userInfo
}
