export type BridgeResponse<T> = {
  /** 状态码 0-成功 */
  code: number
  /** 消息文案 */
  message: string
  /** 数据 */
  data: T
}

/** 获取状态栏高度、底部安全区域高度的回调函数 */
export type StatusBarHeightCallback = (res: BridgeResponse<{ top: number; bottom: number }>) => void

export interface BridgeNativeModule {
  /**
   * 获取设备状态栏高度、底部安全区域高度
   */
  containerViewInset: (params: Record<string, unknown>, callback: StatusBarHeightCallback) => void
  // moonBridge 使用 js 编写, 没有类型提示, 我们使用其方法时手动添加类型, 减少ts类型提示
  // [key: string]: (...args: any[]) => any
}
