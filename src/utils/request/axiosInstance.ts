import Axios, { type AxiosError, type AxiosResponse, type InternalAxiosRequestConfig } from 'axios'
import { judgeApiSuccess } from '../common'
import { getAppInfo, getUserInfo } from '../bridge'

// 定义后端返回数据的通用结构
interface ApiResponse<T = any> {
  code: number | string
  status: number | string
  msg: string
  success?: boolean
  data: T
}

const axiosInstance = Axios.create({
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 12000,
  baseURL: import.meta.env.VITE_BASEURL,
  withCredentials: true,
})

// 请求拦截器
axiosInstance.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const commonParams = {
      ...(getAppInfo() || {}),
      ...(getUserInfo() || {}),
    } as ReturnType<typeof getAppInfo> & ReturnType<typeof getUserInfo>

    // 统一注入应用信息、用户信息这类公参
    config.params = config.params || {}
    commonParams.currentStudentCode = config.params?.studentCode || commonParams.currentStudentCode
    config.params = { ...commonParams, ...config.params }

    return config
  },
  (error: AxiosError) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
axiosInstance.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    const isSuccess = judgeApiSuccess(response.data)
    if (!isSuccess) {
      // todo 增加错误处理?? code=999999 等登录信息失效情况情况是否可以在这里统一处理？
      return Promise.reject(new Error(response.data.msg || '请求失败'))
    }
    return response.data as any
  },
  (error: AxiosError) => {
    console.error('网络错误:', error)
    return Promise.reject(error)
  }
)

export default axiosInstance
