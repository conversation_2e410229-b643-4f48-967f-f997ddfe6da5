import type { AxiosRequestConfig, AxiosResponse } from 'axios'
import axiosInstance from './axiosInstance'

/**
 * GET 请求
 * @param url 请求地址
 * @param params 请求参数
 * @param config 请求配置
 * @returns 请求结果
 */
export const get = <T = any>(
  url: string,
  params?: any,
  config?: AxiosRequestConfig
): Promise<AxiosResponse<T>> => {
  return axiosInstance.get<T>(url, { params, ...config })
}

/**
 * POST 请求
 * @param url 请求地址
 * @param data 请求数据
 * @param config 请求配置
 * @returns 请求结果
 */
export const post = <T = any>(
  url: string,
  data?: any,
  config?: AxiosRequestConfig
): Promise<AxiosResponse<T>> => {
  return axiosInstance.post<T>(url, data, config)
}

/**
 * PUT 请求
 * @param url 请求地址
 * @param data 请求数据
 * @param config 请求配置
 * @returns 请求结果
 */
export const put = <T = any>(
  url: string,
  data?: any,
  config?: AxiosRequestConfig
): Promise<AxiosResponse<T>> => {
  return axiosInstance.put<T>(url, data, config)
}

/**
 * DELETE 请求
 * @param url 请求地址
 * @param data 请求数据
 * @param config 请求配置
 * @returns 请求结果
 */
export const del = <T = any>(
  url: string,
  data?: any,
  config?: AxiosRequestConfig
): Promise<AxiosResponse<T>> => {
  return axiosInstance.delete<T>(url, { data, ...config })
}
