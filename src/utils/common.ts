import QS from 'qs'

/**
 * 后端无法统一接口成功标识，前端统一判断，命中一种即为成功
 * @param {any} res 接口返回结果
 * @param {Array<string | number>} apiSuccessCode 接口成功 code
 * @returns {boolean} 是否成功
 */
export const judgeApiSuccess = (res: any, apiSuccessCode?: (string | number)[]) => {
  if (!res || res?.success === false) return false

  const successCodeArray = [...(apiSuccessCode || []), '100000', 100000, '200', 200, 0]
  return successCodeArray.includes(res.code) || successCodeArray.includes(res.status)
}

/**
 * 解析 URL 参数
 * @returns {Object} 解析后的参数对象
 * @example
 * parseURLParams() // { a: '1', b: '2' }
 */
export const parseURLParams = () => {
  return QS.parse(decodeURI(window.location.search), { ignoreQueryPrefix: true })
}
